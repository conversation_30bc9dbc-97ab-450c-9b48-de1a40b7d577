import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AnalyzedFilesTable } from "@/components/tables/AnalyzedFilesTable";
import { FileUpload } from "@/components/FileUpload";
import { UploadedFilesDisplay } from "../components/UploadedFilesDisplay";
import { UI_CONSTANTS } from "@/constants";

export function HomePage() {
  const [refreshKey, setRefreshKey] = useState(0);

  const handleUploadComplete = () => {
    setRefreshKey((prev) => prev + 1);
  };

  return (
    <div className="space-y-6">
      <AnalyzedFilesTable key={`analyzed-${refreshKey}`} />

      <div className="flex justify-center gap-4 py-4">
        <Button className={UI_CONSTANTS.ACTION_BUTTON_CLASSES}>Review Waveforms</Button>
        <Button className={UI_CONSTANTS.ACTION_BUTTON_CLASSES}>View Report</Button>
        <Button className={UI_CONSTANTS.ACTION_BUTTON_CLASSES}>View Images</Button>
        <Button className={UI_CONSTANTS.ACTION_BUTTON_CLASSES}>Delete Report/Images</Button>
      </div>

      <UploadedFilesDisplay refreshTrigger={refreshKey} />

      <FileUpload onUploadComplete={handleUploadComplete} />
    </div>
  );
}
